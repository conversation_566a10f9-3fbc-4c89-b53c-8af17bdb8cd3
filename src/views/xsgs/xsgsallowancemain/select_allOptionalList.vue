<template>
    <a-tabs default-active-key="1" >
        <a-tab-pane key="1" tab="选型、选装、基本型配置">
            <div style="float: left;width: 70%;">
                <table style="width: 100%; border: 1px solid #f0f0f0;" >
                    <tbody class="ant-table-tbody">
                    <tr>
                        <td colspan="5">选型、选装配置及单价</td>
                        <td>选配件金额</td>
                        <td>{{optionAmount}}</td>
                    </tr>
                    <tr>
                        <td>类别</td>
                        <td>名称</td>
                        <td>规格</td>
                        <td>供应商</td>
                        <td>单价</td>
                        <td>系列</td>
                        <td>形式</td>
                    </tr>
                    <tr v-for="item in allowanceParts"  v-bind:key="item.id" >
                        <td>{{item.subType}}</td>
                        <td>{{item.partsName}}</td>
                        <td>{{item.specs}}</td>
                        <td>{{item.supplierBak}}</td>
                        <td v-if="item.subType=='选型件'" >{{item.valuePrice}}</td>
                        <td v-else >{{item.actualPrice}}</td>
                        <td>{{item.series}}</td>
                        <td>{{item.form}}</td>
                    </tr>
                    <tr>
                        <td colspan="7">
                            不带
                            <a @click="toggleAdvanced" style="margin-left: 8px">
                                {{ advanced ? '收起' : '展开' }}
                                <a-icon :type="advanced ? 'up' : 'down'"/>
                            </a>
                        </td>
                    </tr>
                    <tr v-for="item in allowanceNoInParts"  v-bind:key="item.id"   v-bind:class="[isActive ?'activeClass':'']">
                        <td>{{item.mainType}}</td>
                        <td>{{item.partsName}}</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td>{{item.series}}</td>
                        <td>{{item.form}}</td>
                    </tr>

                    <tr>
                        <td colspan="7">基本型配置取消及单价</td>
                    </tr>
                    <tr v-for="item in allowanceSubtractionParts"  v-bind:key="item.id" >
                        <td>{{item.subType}}</td>
                        <td>{{item.partsName}}</td>
                        <td>{{item.specs}}</td>
                        <td>{{item.supplierBak}}</td>
                        <td>{{item.actualPrice}}</td>
                        <td>{{item.series}}</td>
                        <td>{{item.form}}</td>
                    </tr>


                    </tbody>
                </table>
            </div>
            <div style="width: 30%; float: left">
                <table style="width: 100%; border: 1px solid #f0f0f0;"  >
                    <tbody class="ant-table-tbody">
                    <tr>
                        <td colspan="2">基本型配置</td>
                    </tr>
                    <tr>
                        <td>名称</td>
                        <td>规格</td>
                    </tr>
                    <tr>
                        <td colspan="2">本体</td>
                    </tr>
                    <tr v-for="item in allowanceBodyParts"  v-bind:key="item.id" >
                        <td>{{item.partsName}}</td>
                        <td>{{item.specs}}</td>
                    </tr>

                    <tr>
                        <td colspan="2">后处理配置</td>
                    </tr>
                    <tr v-for="item in allowanceReprocessingParts"  v-bind:key="item.id" >
                        <td>{{item.partsName}}</td>
                        <td>{{item.specs}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>


        </a-tab-pane>
        <a-tab-pane key="2" tab="备注信息">
          <a-input type="textarea" style="background: white;color: #000000A6" :value="remarks" disabled/>
        </a-tab-pane>
    </a-tabs>



</template>

<script>
    import {LISTALL_XSGSALLOWANCEITEM} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    import ATextarea from "ant-design-vue/es/input/TextArea";
    export default {
        name: "select_allOptionalList",
        props:{
            record:Object,
        },
        data() {
            return {
                isActive:true,
                advanced:false,
                optionAmount:0,
                remarks:null,
                allowanceParts:[],
                allowanceSubtractionParts:[],
                allowanceNoInParts:[],
                allowanceReprocessingParts:[],
                allowanceBodyParts:[],
            }
        },
        mounted() {
            this.checkName();
        },
        methods: {
            toggleAdvanced () {
                this.advanced = !this.advanced
                this.isActive = !this.isActive
            },
            getAllData: function () {
                this.allowanceParts=[]
                this.allowanceSubtractionParts=[]
                this.allowanceNoInParts=[]
                this.allowanceReprocessingParts=[]
                this.allowanceBodyParts=[]
                this.optionAmount=0
                this.remarks=null
                request(LISTALL_XSGSALLOWANCEITEM, METHOD.POST, this.record).then(res => {
                        this.allowanceParts=res.data.data.allowanceParts
                        this.allowanceSubtractionParts=res.data.data.allowanceSubtractionParts
                        this.allowanceReprocessingParts=res.data.data.allowanceReprocessingParts
                        this.allowanceNoInParts=res.data.data.allowanceNoInParts
                        this.allowanceBodyParts=res.data.data.allowanceBodyParts
                        this.optionAmount=res.data.data.optionAmount
                        this.remarks=res.data.data.remarks
                    })
            }


        }
    }
</script>

<style scoped>
    /deep/.ant-table-tbody > tr > td {
        border: 1px solid #464646;
        border-right: none;
    }
    /deep/.ant-tabs .ant-tabs-left-bar .ant-tabs-tab, .ant-tabs .ant-tabs-right-bar .ant-tabs-tab {
        display: block;
        float: none;
        margin: 0 0 16px 0;
        padding: 20px 10px;
    }
    td{
        border: 1px solid #f0f0f0;
        text-align: center;
    }
    .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
        padding: 1px 1px !important;
        overflow-wrap: break-word !important;
    }
    .activeClass{
        display: none;
    }


</style>