<template>
  <div>
    <a-card>
      <a-form-model
        ref="searchForm"
        :model="searchForm"
        :labelCol="{ span: 8 }"
        :wrapperCol="{ span: 16 }"
      >
        <table style="width: 100%; border: 1px solid #f0f0f0">
          <tbody class="ant-table">
            <tr>
              <td><a-form-model-item label="年度" /></td>
              <td style="width: 50px"><a-input v-model="searchForm.year" /></td>

              <td><a-form-model-item label="客户" /></td>
              <td>
                <a-input v-model="searchForm.customer" @blur="customerBlurs" />
              </td>

              <td><a-form-model-item label="客户名称" /></td>
              <td colspan="3">
                <a-select
                  v-model="searchForm.customerName"
                  show-search
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :allowClear="true"
                  @search="handleCustomerSearch"
                  @change="handleCustomerChange"
                >
                  <a-select-option v-for="d in customerList" :key="d.name">
                    {{ d.name }}
                  </a-select-option>
                </a-select>
              </td>

              <td><a-form-model-item label="产品型号" /></td>
              <td>
                <a-select
                  v-model="productModel"
                  mode="multiple"
                  style="min-width: 150px"
                >
                  <a-select-option v-for="sty in zcpxh_list" :key="sty">
                    {{ sty }}
                  </a-select-option>
                </a-select>
              </td>

              <td><a-form-model-item label="状态机" /></td>
              <td>
                <a-input v-model.trim="searchForm.machineType" />
              </td>
            </tr>
            <tr>
              <td><a-form-model-item label="排放" /></td>
              <td>
                <a-select
                  v-model="blowoff"
                  mode="multiple"
                  style="min-width: 80px"
                >
                  <a-select-option value="国6">国6</a-select-option>
                  <a-select-option value="国5">国5</a-select-option>
                  <a-select-option value="国4">国4</a-select-option>
                  <a-select-option value="国3">国3</a-select-option>
                  <a-select-option value="国2">国2</a-select-option>
                  <a-select-option value="国1">国1</a-select-option>
                </a-select>
              </td>

              <td><a-form-model-item label="板块" /></td>
              <td>
                <a-select
                  v-model="plate"
                  mode="multiple"
                  style="min-width: 120px"
                >
                  <a-select-option value="卡车">卡车</a-select-option>
                  <a-select-option value="客车">客车</a-select-option>
                  <a-select-option value="新能源">新能源</a-select-option>
                </a-select>
              </td>

              <td><a-form-model-item label="系列" /></td>
              <td>
                <a-select
                  v-model="series"
                  mode="multiple"
                  style="min-width: 150px"
                >
                  <a-select-option v-for="sty in zxl_list" :key="sty">
                    {{ sty }}
                  </a-select-option>
                </a-select>
              </td>

              <td><a-form-model-item label="功率(PS)" /></td>
              <td>
                <a-select v-model="ps" mode="multiple" style="min-width: 150px">
                  <a-select-option v-for="sty in zgl_list" :key="sty">
                    {{ sty }}
                  </a-select-option>
                </a-select>
              </td>

              <td>创建日期</td>
              <td colspan="3">
                <a-form-item
                  :style="{ display: 'inline-block', width: 'calc(50% - 6px)' }"
                >
                  <a-date-picker
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                    placeholder=""
                    v-model="searchForm.createTimeStart"
                  />
                </a-form-item>
                <span
                  :style="{
                    display: 'inline-block',
                    width: '12px',
                    textAlign: 'center',
                  }"
                  >-</span
                >
                <a-form-item
                  :style="{ display: 'inline-block', width: 'calc(50% - 6px)' }"
                >
                  <a-date-picker
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                    placeholder=""
                    v-model="searchForm.createTimeEnd"
                  />
                </a-form-item>
              </td>
            </tr>
            <tr>
              <td colspan="12" align="center">
                <a-button
                  type="primary"
                  style="margin: 0 8px"
                  @click="handleSearch"
                  ><a-icon type="search" />查询</a-button
                >
                <a-button
                  type="primary"
                  style="margin: 0 8px"
                  @click="resetQuery"
                  ><a-icon type="reload" />重置</a-button
                >
              </td>
            </tr>
          </tbody>
        </table>
      </a-form-model>

      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button
              v-if="checkPf('PRICE_MSG_ADD')"
              type="primary"
              style="margin: 0 8px"
              @click="handleNew"
              ><a-icon type="plus" />新增</a-button
            >
            <a-button
              v-if="checkPf('PRICE_MSG_DELETE')"
              type="danger"
              style="margin: 0 8px"
              @click="deleteSelectedIds"
              ><a-icon type="delete" />删除</a-button
            >
            <a-modal v-model="importModalVisible" title="选择导入类型">
              <div style="text-align: center; padding: 20px">
                <div
                  style="
                    margin: 20px 0;
                    display: flex;
                    gap: 10px;
                    justify-content: center;
                    flex-wrap: wrap;
                  "
                >
                  <a-upload
                    ref="newPriceUpload"
                    name="file"
                    :action="UPLOAD_XSGSALLOWANCEMAINNew"
                    :headers="{ Authorization: Cookie.get('Authorization') }"
                    :data="{ type: 'sales', templateType: 'new' }"
                    :showUploadList="false"
                    @change="handleUploadExcel"
                  >
                    <a-button
                      type="primary"
                      size="small"
                      style="margin-right: 10px; width: 140px"
                    >
                      新模板价格导入
                    </a-button>
                  </a-upload>
                  <a-upload
                    ref="oldPriceUpload"
                    name="file"
                    :action="UPLOAD_XSGSALLOWANCEMAIN"
                    :headers="{ Authorization: Cookie.get('Authorization') }"
                    :data="{ type: 'sales', templateType: 'old' }"
                    :showUploadList="false"
                    @change="handleUploadExcel"
                  >
                    <a-button
                      type="primary"
                      size="small"
                      style="margin-right: 10px; width: 140px"
                    >
                      旧模板价格导入
                    </a-button>
                  </a-upload>
                </div>
                <div>
                  <a-upload
                    ref="salesUpload"
                    name="file"
                    :action="IMPORT_XSGSALLOWANCEITEM"
                    :headers="{ Authorization: Cookie.get('Authorization') }"
                    :data="{ type: 'sales' }"
                    :showUploadList="false"
                    @change="handleUploadExcel"
                  >
                    <a-button
                      type="primary"
                      size="small"
                      style="margin-right: 10px; width: 120px"
                    >
                      销量导入
                    </a-button>
                  </a-upload>
                  <a-upload
                    ref="revenueUpload"
                    name="file"
                    :action="IMPORT_XSGSALLOWANCEITEM_INCOME"
                    :headers="{ Authorization: Cookie.get('Authorization') }"
                    :data="{ type: 'revenue' }"
                    :showUploadList="false"
                    @change="handleUploadExcel"
                  >
                    <a-button type="primary" size="small" style="width: 120px"
                      >收入导入</a-button
                    >
                  </a-upload>
                </div>
                <div
                  style="
                    margin: 20px 0;
                    display: flex;
                    gap: 10px;
                    justify-content: center;
                    flex-wrap: wrap;
                  "
                >
                  <!-- <a-button
                    size="small"
                    type="primary"
                    style="margin: 0 4px"
                    @click="handleDownloadExcel('newPrice')"
                  >
                    <a-icon type="download" />
                    下载新模板价格导入模板
                  </a-button>

                  <a-button
                    size="small"
                    type="primary"
                    style="margin: 0 4px"
                    @click="handleDownloadExcel('oldPrice')"
                  >
                    <a-icon type="download" />
                    下载旧模板价格导入模板
                  </a-button> -->

                  <a-button
                    size="small"
                    type="primary"
                    style="margin: 0 4px"
                    @click="handleDownloadExcel('sale')"
                  >
                    <a-icon type="download" />
                    下载销量导入模板
                  </a-button>

                  <a-button
                    size="small"
                    type="primary"
                    style="margin: 0 4px"
                    @click="handleDownloadExcel('income')"
                  >
                    <a-icon type="download" />
                    下载收入导入模板
                  </a-button>
                </div>
              </div>
              <template #footer>
                <a-button @click="importModalVisible = false">取消</a-button>
              </template>
            </a-modal>

            <a-button
              v-if="checkPf('PRICE_MSG_IMPORT')"
              type="primary"
              style="margin: 0 8px"
              @click="importModalVisible = true"
            >
              <a-icon type="upload" />
              客户数据导入
            </a-button>

            <!--            <a-button type="primary" style="margin: 0 8px" @click="updateOrTouch">更新物料信息</a-button>-->
            <!--            <a-button type="primary" style="margin: 0 8px" @click="touch">对碰基本型信息</a-button>-->
          </a-form-model-item>
        </a-col>
      </a-row>

      <vxe-grid
        highlight-hover-row
        row-id="id"
        ref="xTable"
        highlight-current-row
        highlight-current-column
        :checkbox-config="{ highlight: true, reserve: true, range: true }"
        :seq-config="{
          startIndex:
            (gridOptions.pagerConfig.currentPage - 1) *
            gridOptions.pagerConfig.pageSize,
        }"
        stripe
        :row-style="rowStyle"
        v-bind="gridOptions"
        :span-method="colspanMethod"
        @page-change="handlePageChange"
      >
        <template #action="{ row }">
          <a v-if="checkPf('PRICE_MSG_EDIT')" @click="handleEdit(row)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleLook(row)">查看</a>
          <a-divider v-if="checkPf('PRICE_MSG_COPY')" type="vertical" />
          <a v-if="checkPf('PRICE_MSG_COPY')" @click="copyEdit(row)">复制</a>
          <a-divider v-if="checkPf('PRICE_MSG_DELETE')" type="vertical" />
          <a v-if="checkPf('PRICE_MSG_DELETE')" @click="handleDelete(row)"
            >删除</a
          >
        </template>
        <template slot="ps" slot-scope="{ row }">
          {{ row.ps ? row.ps.split(".")[0] : "" }}
        </template>
      </vxe-grid>
    </a-card>
  </div>
</template>

<script>
import { hasAuth } from "@/utils/authority-utils";
import {
  LIST_XSGSALLOWANCEMAIN_PAGE,
  SUBMIT_XSGSALLOWANCEMAIN,
  DOWNLOAD_XSGSALLOWANCEMAIN,
  DELETEBYID_XSGSALLOWANCEMAIN,
  UPDATEORTOUCH_XSGSALLOWANCEMAIN,
  TOUCH_XSGSALLOWANCEMAIN,
  LIST_XSGSCUSTOMER,
  UPLOAD_XSGSALLOWANCEMAIN,
  DELETEBYIDS_XSGSALLOWANCEMAIN,
  SUBMIT_XSGSALLOWANCEMAIN_COPY,
  GETSEACHLIST_XSGSALLOWANCEMAIN,
  DOWNLOAD_INCOME_XSGSALLOWANCEMAIN,
  DOWNLOAD_SALE_XSGSALLOWANCEMAIN,
  DOWNLOAD_NEWPRICE_XSGSALLOWANCEMAIN,
  DOWNLOAD_OLDPRICE_XSGSALLOWANCEMAIN,
  IMPORT_XSGSALLOWANCEITEM,
  IMPORT_XSGSALLOWANCEITEM_INCOME,
  UPLOAD_XSGSALLOWANCEMAINNew,
} from "@/services/api/xsgs";
import { METHOD, request, exportExcel } from "@/utils/request";
import tableDragResize from "@/utils/tableDragResize";

import Cookie from "js-cookie";
export default {
  name: "Manger.vue",
  mixins: [tableDragResize],
  data() {
    return {
      importModalVisible: false,
      hasAuth,
      UPLOAD_XSGSALLOWANCEMAIN,
      IMPORT_XSGSALLOWANCEITEM,
      IMPORT_XSGSALLOWANCEITEM_INCOME,
      UPLOAD_XSGSALLOWANCEMAINNew,
      Cookie,
      loading: false,
      dialog: false,
      // 字段权限关键字
      TF_FIELD: "PRICE_MANAGER,PRICE_MSG_TF",
      // 功能权限关键字
      PF_FIELD: "PRICE_MANAGER,PRICE_MSG_PF",
      PF_LIST: [],
      confirmLoading: false,
      fullPath: null,
      searchForm: {
        customer: null,
        customerName: null,
      },

      productModel: [],
      machineType: [],
      blowoff: [],
      plate: [],
      series: [],
      ps: [],

      zcpxh_list: [], //产品型号
      zztj_list: [], //状态机
      zxl_list: [], //系列
      zgl_list: [], //功率

      customerList: [],
      selectedRowKeys: [],
      form: {},
      initColumns: [
        {
          type: "checkbox",
          title: "",
          align: "center",
          width: 35,
          fixed: "left",
          exclude: true,
        },
        {
          title: "序号",
          align: "center",
          type: "seq",
          width: 40,
          fixed: "left",
          exclude: true,
        },
        { title: "年度", field: "year", align: "center", width: 40 },
        { title: "客户", field: "customer", align: "center", width: 50 },
        {
          title: "客户名称",
          field: "customerName",
          align: "center",
          width: 200,
        },
        { title: "状态机", field: "machineType", align: "center", width: 120 },
        {
          title: "产品型号",
          field: "productModel",
          align: "center",
          width: 110,
        },
        { title: "排放", field: "blowoff", align: "center", width: 40 },
        { title: "板块", field: "plate", align: "center", width: 40 },
        { title: "系列", field: "series", align: "center", width: 40 },
        {
          title: "功率(PS)",
          field: "ps",
          align: "center",
          width: 60,
          slots: { default: "ps" },
        },
        {
          title: "基本型开票价",
          field: "basicAmount",
          align: "center",
          width: 90,
        },
        {
          title: "选配件合计",
          field: "chooseAmount",
          align: "center",
          width: 80,
        },
        {
          title: "整机开票价",
          field: "unitAmount",
          align: "center",
          width: 80,
        },
        { title: "商务核算编号", field: "id", align: "center", width: 110 },
        {
          title: "操作",
          width: 190,
          align: "center",
          slots: { default: "action" },
          fixed: "right",
          exclude: true,
        },
      ],
      // 分组列头，通过 children 定义子列
      gridOptions: {
        border: true,
        resizable: true,
        keepSource: true,
        showOverflow: true,
        loading: false,
        pagerConfig: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          pageSizes: [10, 20, 50],
          layouts: [
            "Sizes",
            "PrevJump",
            "PrevPage",
            "Number",
            "NextPage",
            "NextJump",
            "FullJump",
            "Total",
          ],
          perfect: true,
        },
        data: [],
      },

      data: [],
      rules: {
        customer: [
          { required: false, message: "客户不能为空", trigger: "blur" },
        ],
        year: [{ required: false, message: "年度不能为空", trigger: "blur" }],
        reference: [
          { required: false, message: "参考机型不能为空", trigger: "blur" },
        ],
        machineType: [
          { required: false, message: "机型不能为空", trigger: "blur" },
        ],
        productModel: [
          { required: false, message: "产品型号不能为空", trigger: "blur" },
        ],
        blowoff: [
          { required: false, message: "排放不能为空", trigger: "blur" },
        ],
        plate: [{ required: false, message: "板块不能为空", trigger: "blur" }],
        series: [{ required: false, message: "系列不能为空", trigger: "blur" }],
        ps: [{ required: false, message: "功率(PS)不能为空", trigger: "blur" }],
        basicAmount: [
          { required: false, message: "基本型开票价不能为空", trigger: "blur" },
        ],
        chooseAmount: [
          { required: false, message: "选配件合计不能为空", trigger: "blur" },
        ],
        unitAmount: [
          {
            required: false,
            message: "整机开票价(basic_amount+choose_amount)不能为空",
            trigger: "blur",
          },
        ],
        createUser: [
          { required: false, message: "创建人不能为空", trigger: "blur" },
        ],
        createTime: [
          { required: false, message: "创建时间不能为空", trigger: "blur" },
        ],
        updateUser: [
          { required: false, message: "更新人不能为空", trigger: "blur" },
        ],
        updateTime: [
          { required: false, message: "更新时间不能为空", trigger: "blur" },
        ],
        isDelete: [
          { required: false, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  async created() {
    // 获取字段权限
    await this.getUserAuth(this.TF_FIELD);
    // 获取功能权限
    await this.getUserAuth(this.PF_FIELD);

    this.gridOptions.columns = this.coverAuthColumn(
      this.TF_FIELD,
      this.initColumns,
      "field"
    );
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  async mounted() {
    this.fullPath = this.$route.fullPath;
    this.getData();
    this.getSearchList();
    this.checkName();
  },
  methods: {
    handleImport(type) {
      this.importModalVisible = false;
      if (type === "sales") {
        this.$refs.salesUpload.$el.querySelector("input").click();
      } else if (type === "revenue") {
        this.$refs.revenueUpload.$el.querySelector("input").click();
      }
    },
    checkPf(field) {
      return this.PF_LIST.findIndex((s) => s == field) != -1;
    },
    rowStyle(rowObj) {
      let data = rowObj.row;
      if (data.isCheck == 1) {
        return { backgroundColor: "#ffff00" };
      }
    },
    updateOrTouch() {
      request(UPDATEORTOUCH_XSGSALLOWANCEMAIN, METHOD.GET).then(() => {
        this.getData();
        this.$message.info("提交成功");
        this.closeForm();
      });
    },
    touch() {
      request(TOUCH_XSGSALLOWANCEMAIN, METHOD.GET).then(() => {
        this.getData();
        this.$message.info("提交成功");
        this.closeForm();
      });
    },
    getSearchList() {
      request(GETSEACHLIST_XSGSALLOWANCEMAIN, METHOD.GET).then((res) => {
        if (res.data.data.cpxh != null) {
          this.zcpxh_list = res.data.data.cpxh;
        }
        if (res.data.data.ztj != null) {
          this.zztj_list = res.data.data.ztj;
        }
        if (res.data.data.xl != null) {
          this.zxl_list = res.data.data.xl;
        }
        if (res.data.data.gl != null) {
          this.zgl_list = res.data.data.gl;
        }
      });
    },

    customerBlurs() {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer: this.searchForm.customer,
      }).then((res) => {
        if (res.data.data.length > 0) {
          if (this.searchForm.customer != null) {
            if (!this.searchForm.customer.split(" ").join("").length == 0) {
              this.searchForm.customerName = res.data.data[0].name;
            }
          }
        } else {
          this.$message.info("此客户号没找到信息");
        }
      });
    },

    handleCustomerSearch(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name: value,
      }).then((res) => {
        this.customerList = res.data.data;
      });
    },
    handleCustomerChange(value) {
      this.customerList.forEach((item) => {
        if (item.name == value) {
          this.searchForm.customer = item.customer;
        }
      });
    },

    headerCellClassName({ column, columnIndex }) {
      if (column.property === "machineType") {
        return "col-blue";
      }
    },

    colspanMethod({ row, _rowIndex, column, visibleData }) {
      if (column.property != "machineType") {
        return;
      }

      const fields = ["machineType"];
      const cellValue = row[column.property];
      //年度
      const yearValue = row["year"];
      //客户号
      const customerValue = row["customer"];

      if (cellValue && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1];
        let nextRow = visibleData[_rowIndex + 1];
        if (
          prevRow &&
          prevRow[column.property] === cellValue &&
          prevRow["year"] === yearValue &&
          prevRow["customer"] === customerValue
        ) {
          return { rowspan: 0, colspan: 0 };
        } else {
          let countRowspan = 1;
          while (
            nextRow &&
            nextRow[column.property] === cellValue &&
            nextRow["year"] === yearValue &&
            nextRow["customer"] === customerValue
          ) {
            nextRow = visibleData[++countRowspan + _rowIndex];
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 };
          }
        }
      }
    },
    handlePageChange({ currentPage, pageSize }) {
      this.gridOptions.pagerConfig.currentPage = currentPage;
      this.gridOptions.pagerConfig.pageSize = pageSize;
      this.getData();
    },

    getData() {
      this.gridOptions.loading = true;
      let current = this.gridOptions.pagerConfig.currentPage;
      let size = this.gridOptions.pagerConfig.pageSize;

      this.searchForm.productModels = this.productModel.join(",");
      this.searchForm.machineTypes = this.machineType.join(",");
      this.searchForm.blowoffs = this.blowoff.join(",");
      this.searchForm.plates = this.plate.join(",");
      this.searchForm.seriess = this.series.join(",");
      this.searchForm.pss = this.ps.join(",");

      request(LIST_XSGSALLOWANCEMAIN_PAGE, METHOD.GET, {
        ...this.searchForm,
        current,
        size,
      }).then((res) => {
        const { records, total } = res.data.data;
        this.gridOptions.loading = false;
        this.gridOptions.data = records;
        this.gridOptions.pagerConfig.total = parseInt(total);
      });
    },

    deleteSelectedIds() {
      this.selectedRowKeys = [];
      const $table = this.$refs.xTable;
      //当前行选中
      const selectRecords = $table.getCheckboxRecords();
      //已保留选中行
      const selectReserveRecords = $table.getCheckboxReserveRecords();
      if (selectRecords.length <= 0 && selectReserveRecords.length <= 0) {
        this.$message.info("请至少选择一条记录！");
        return;
      }
      selectRecords.forEach((item) => {
        this.selectedRowKeys.push(item.id);
      });
      selectReserveRecords.forEach((item) => {
        this.selectedRowKeys.push(item.id);
      });

      this.$confirm({
        content:
          `是否确认删除选择的` + this.selectedRowKeys.length + `条数据？`,
        onOk: () => {
          this.loading = true;
          request(DELETEBYIDS_XSGSALLOWANCEMAIN, METHOD.POST, {
            ids: this.selectedRowKeys,
          })
            .then(() => {
              this.$message.info("删除成功");
              this.getData();
              this.$refs.xTable.clearCheckboxReserve();
            })
            .catch(() => {
              this.loading = false;
            });
        },
      });
    },

    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          request(SUBMIT_XSGSALLOWANCEMAIN, METHOD.POST, this.form).then(() => {
            this.getData();
            this.$message.info("提交成功");
            this.closeForm();
          });
        }
      });
    },

    /** 新增，打开对话框 */
    handleNew() {
      let query = {
        option: 0,
        parentPath: this.fullPath,
      };
      this.$router.push({
        path: "/xsgs_allowance_main/:id",
        props: true,
        query: { ...query },
      });
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      let query = {
        option: 1,
        parentPath: this.fullPath,
      };
      this.$router.push({
        path: "/xsgs_allowance_main/" + record.id,
        props: true,
        query: { ...query },
      });
    },
    /** 查看，打开对话框 */
    handleLook(record) {
      this.$router.push({
        path: "/xsgs_allowance_mainSee/" + record.id,
        props: true,
      });
    },
    copyAction(record) {
      this.loading = true;
      request(SUBMIT_XSGSALLOWANCEMAIN_COPY, METHOD.POST, {
        id: record.id,
      }).then(() => {
        this.getData();
        this.loading = false;
        this.$message.success("复制成功");
      });
    },
    copyEdit(record) {
      // let self = this
      // this.$confirm({
      //   title: '提示',
      //   content: '是否复制当前数据?',
      //   onOk() {
      //     self.copyAction(record)
      //   },
      //   onCancel() {},
      // });
      this.$router.push({
        path:
          "/xsgs_allowance_main/" +
          record.id +
          "?copyMachineType=" +
          record.machineType,
        props: true,
        query: { option: 2 },
      });
    },
    handleDelete(record) {
      let self = this;
      this.$confirm({
        title: "提示",
        content: "是否删除当前数据?",
        onOk() {
          self.deleteAction(record);
        },
        onCancel() {},
      });
    },
    deleteAction(record) {
      this.loading = true;
      request(DELETEBYID_XSGSALLOWANCEMAIN, METHOD.POST, {
        id: record.id,
      }).then(() => {
        this.getData();
        this.loading = false;
        this.$message.success("删除成功");
      });
    },

    /** 处理查询 */
    handleSearch() {
      this.gridOptions.pagerConfig.currentPage = 1;
      this.getData();
    },
    /** 重置按钮操作 */
    resetQuery() {
      (this.productModel = []),
        (this.machineType = []),
        (this.blowoff = []),
        (this.plate = []),
        (this.series = []),
        (this.ps = []),
        (this.searchForm = {
          customerName: null,
          customer: null,
        });
      this.$refs.xTable.clearCheckboxReserve();
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false;
      this.$refs.form.resetFields();
      this.form = {};
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.gridOptions.loading = true;
      if (info.file.status !== "uploading") {
        this.getData();
      }
      if (info.file.status === "done") {
        this.gridOptions.loading = false;
        if (info.file.response.msg === "FAIL") {
          // this.$message.error(`${info.file.name} 导入失败`);
          if (info.file.response.data.length > 0) {
            let mesage = "";
            info.file.response.data.forEach((item) => {
              mesage += item + "\n";
            });
            this.$warning({
              title: "导入失败提示",
              content: <div style="white-space: pre-wrap;">{mesage}</div>,
              width: 445,
            });
          }
        } else {
          this.$message.success(`${info.file.name} 导入成功`);
          this.importModalVisible = false;
        }
      } else if (info.file.status === "error") {
        if (info.file.response && info.file.response.msg) {
          this.$message.error(info.file.response.msg);
        } else {
          this.$message.error(`${info.file.name} 导入失败`);
          this.gridOptions.loading = false;
        }
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      this.$confirm({
        title: "提示",
        content: "是否确认导出所有信息数据项?",
        onOk() {
          window.open(decodeURI(DOWNLOAD_XSGSALLOWANCEMAIN));
        },
        onCancel() {},
      });
    },
    /**   下载按钮操作 */
    handleDownloadExcel(type) {
      let url;
      let fileName;

      switch (type) {
        case "income":
          url = DOWNLOAD_INCOME_XSGSALLOWANCEMAIN;
          fileName = "收入导入模板.xlsx";
          break;
        case "sale":
          url = DOWNLOAD_SALE_XSGSALLOWANCEMAIN;
          fileName = "销量导入模板.xlsx";
          break;
        case "newPrice":
          url = DOWNLOAD_NEWPRICE_XSGSALLOWANCEMAIN;
          fileName = "新模板价格导入模板.xlsx";
          break;
        case "oldPrice":
          url = DOWNLOAD_OLDPRICE_XSGSALLOWANCEMAIN;
          fileName = "旧模板价格导入模板.xlsx";
          break;
        default:
          url = DOWNLOAD_SALE_XSGSALLOWANCEMAIN;
          fileName = "销量导入模板.xlsx";
      }

      console.log(url, "参数", type);
      exportExcel(url, {}, fileName);
    },
  },
};
</script>

<style scoped>
.ant-table-thead > tr > th,
.ant-table-tbody > tr > /deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/*/deep/  样式穿透
  */
/deep/.ant-col-8 {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}

/deep/.col-blue {
  background-color: #2db7f5;
  color: #fff;
}

/deep/.vxe-table--render-default .vxe-body--column.col--ellipsis,
.vxe-table--render-default.vxe-editable .vxe-body--column,
.vxe-table--render-default .vxe-footer--column.col--ellipsis,
.vxe-table--render-default .vxe-header--column.col--ellipsis {
  height: 0px;
}
/deep/.vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 0px 0;
}
/deep/.vxe-table--render-default.border--full .vxe-body--column,
/deep/ .vxe-table--render-default.border--full .vxe-footer--column,
/deep/.vxe-table--render-default.border--full .vxe-header--column {
  background-image: -webkit-gradient(
      linear,
      left top,
      left bottom,
      from(#464646),
      to(#464646)
    ),
    -webkit-gradient(linear, left top, left bottom, from(#464646), to(#464646));
  background-image: linear-gradient(#464646, #464646),
    linear-gradient(#464646, #464646);
  background-repeat: no-repeat;
  background-size: 1px 100%, 100% 1px;
  background-position: 100% 0, 100% 100%;
}

/deep/.vxe-table--render-default.border--default .vxe-table--header-wrapper,
.vxe-table--render-default.border--full .vxe-table--header-wrapper,
.vxe-table--render-default.border--outer .vxe-table--header-wrapper {
  background-color: #f8f8f9;
}

/deep/ .vxe-pager.is--perfect {
  border: 0px solid #464646;
  border-top-width: 0;
  background-color: #fff;
}
/deep/ .vxe-header--column {
  background-color: rgba(0, 0, 0, 0.2);
}
/deep/ .vxe-table--render-default .vxe-body--row.row--stripe {
  background-color: #e6fdff;
}
/deep/ .vxe-table--render-default .vxe-body--row.row--checked {
  background-color: #fff3e0;
}
/deep/.ant-col-16 {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.vxe-table--render-default .vxe-body--row.row--hover {
  background-color: #fcce10;
}
/deep/.vxe-table--render-default .vxe-body--row.row--current {
  background-color: #fcce10;
}
/deep/.vxe-table--render-default .vxe-body--column.col--current {
  background-color: #fcce10;
}
/deep/.vxe-table--render-default .vxe-header--column.col--current {
  background-color: #fcce10;
}
/deep/ .vxe-table--render-default .vxe-cell {
  padding: 0px;
}
/deep/ .vxe-table--header-wrapper .vxe-table--render-default .vxe-cell {
  text-align: center !important;
}
/deep/ .vxe-header--row > th {
  padding: 0;
}

/deep/ .vxe-table--render-default.border--full .vxe-header--column {
  background-image: none !important;
  border: 0.5px solid;
}
/deep/ .vxe-table--render-default.border--full .vxe-body--column {
  background-image: none !important;
  border: 0.5px solid;
}
/deep/ .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 8px 0;
}
/deep/ .vxe-table--main-wrapper {
  border: 1px solid;
}
/deep/ .vxe-table--fixed-left-wrapper {
  border-left: 1px solid;
  border-top: 1px solid;
}
/deep/ .vxe-table--fixed-right-wrapper {
  border-right: 1px solid;
  border-top: 1px solid;
}
</style>
